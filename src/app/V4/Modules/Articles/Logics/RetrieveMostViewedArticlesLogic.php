<?php

namespace App\V4\Modules\Articles\Logics;

use App\V4\Repositories\ArticleRepository;
use Illuminate\Http\JsonResponse;

class RetrieveMostViewedArticlesLogic
{
    private ArticleRepository $articleRepository;

    public function __construct(
        ArticleRepository $articleRepository
    )
    {
        $this->articleRepository = $articleRepository;
    }
    public function execute(string $fromDate, string $toDate, ?array $language, ?string $uniqueId, int $page): JsonResponse
    {
        $data = $this->articleRepository->getMostViewedArticles($fromDate, $toDate, $language, $uniqueId, $page);
        return response()->json($data);
    }
}