<?php

namespace App\V4\Modules\PN2_0\Logics;

use App\Services\GeminiApi\Services\SuggestsPNTitleAndDescription;
use App\V4\Exceptions\ArticleNotFoundException;
use App\V4\Exceptions\NwForbiddenExceptionV4;
use App\V4\Models\Article;
use App\V4\Repositories\ArticleRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class GetPNTitleAndDescriptionSuggestionLogic
{
    private SuggestsPNTitleAndDescription $suggestsPNTitleAndDescription;
    private ArticleRepository $articleRepository;

    public function __construct(
        SuggestsPNTitleAndDescription $suggestsPNTitleAndDescription,
        ArticleRepository $articleRepository
    ) {
        $this->suggestsPNTitleAndDescription = $suggestsPNTitleAndDescription;
        $this->articleRepository = $articleRepository;
    }

    /**
     * @throws ArticleNotFoundException
     * @throws NwForbiddenExceptionV4
     * @throws ModelNotFoundException
     *
     */
    public function execute(string $uniqueId) : array
    {
        $article = $this->articleRepository->findBy('uniqueId', $uniqueId);

        if (empty($article)) {
            throw new ArticleNotFoundException();
        }

        $currentArticleLanguage = $article->channel->language;

        return $this->getSuggestions($article, $currentArticleLanguage);

    }

    private function getSuggestions(Article $article, string $currentLanguage): array
    {
        $suggestionsText = $this->suggestsPNTitleAndDescription->execute(
            $article->html,
            $currentLanguage
        );

        preg_match('/\{.*\}/s', $suggestionsText, $matches);

        return json_decode($matches[0], true);
    }
}
